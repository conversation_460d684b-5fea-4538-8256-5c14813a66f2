# FROM 446567516155.dkr.ecr.ap-southeast-1.amazonaws.com/docker-image-common-alpine
# FROM node:16-alpine
#FROM 446567516155.dkr.ecr.ap-southeast-1.amazonaws.com/docker-image-common-alpine:latest
FROM els-registry.evnfc.vn/common/docker-image-common-alpine:latest

RUN apk --no-cache add bash libreoffice 

ENV NODE_ENV=uat-cloud
# Create app directory
WORKDIR /app

# Install app dependencies
# A wildcard is used to ensure both package.json AND package-lock.json are copied
# where available (npm@5+)
# COPY package*.json ./
COPY . .

#RUN npm install
RUN npm install
VOLUME ["/var/convert-service"]

# Bundle app source
# RUN npm run build

CMD [ "npm", "run", "uat-cloud" ]

