def registryCredential = "harbor_user"
pipeline {
    agent any

    options {
        buildDiscarder(logRotator(numToKeepStr: '3'))
    }

    environment {
        PROJECT = "els"
        APP_NAME = "convert-service" //Replace
        HARBOR_URL = "https://els-registry.evnfc.vn"
        SONAR_HOST_URL = "http://sonarqube.evnfc.vn"		
		projectKey = "els_convert-service_aa3e8dba-2e1e-4626-9fba-1f92f575084c"
        SONAR_TOKEN = "sqp_cdfcce25381b75fde47278e7bcaa970b15978773"
    }


    stages {
        stage("Checkout code") {
            steps {
              checkout scm
            }
        }

        stage("Trivy FS Scan") {
            steps {
                sh '''
                trivy fs --severity CRITICAL,HIGH --ignore-unfixed --scanners vuln,license,secret  --format table .
                '''
            }
        }

        stage("Trivy Config Scan") {
            steps {
                sh '''
                trivy config --severity CRITICAL,HIGH --format table  .
                '''
            }
        }
        stage("SonarQube Scan") {
            steps {
                    sh '''
                    export PATH=$PATH:/opt/sonar-scanner-5.0.1.3006-linux/bin
                    sonar-scanner \
                      -Dsonar.projectKey=${projectKey} \
                      -Dsonar.sources=. \
                      -Dsonar.tests=src/test/java,src/test/resources,tests \
                      -Dsonar.host.url=${SONAR_HOST_URL} \
                      -Dsonar.login=${SONAR_TOKEN}
                    '''
            }
        }
        stage("Build image") {

            steps {

                script {
                    imageId = "els-registry.evnfc.vn/${PROJECT}/${APP_NAME}-${BRANCH_NAME}:$BUILD_NUMBER"
                    docker.withRegistry(HARBOR_URL, registryCredential) {
                        myapp = docker.build(imageId, "-f Dockerfile .")
                        myapp.push()
                    }
                }
            }
        }
        stage("Trivy Image Scan") {
            steps {
                script {
                    def imageFull = "els-registry.evnfc.vn/${PROJECT}/${APP_NAME}-${BRANCH_NAME}:$BUILD_NUMBER"
                    sh """
                    trivy image --severity CRITICAL,HIGH --format table  ${imageFull}
                    """
                }
            }
        }

        stage("Vulnerability Scan") {
            steps {
                script {
                    def IMAGE = "els-registry.evnfc.vn/${PROJECT}/${APP_NAME}-${BRANCH_NAME}:$BUILD_NUMBER"
                    sh 'curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/html.tpl > html.tpl'
                    //sh "/usr/local/bin/trivy image --output trivy_report.html --format template --template '@html.tpl' --timeout 10m --severity CRITICAL --light ${IMAGE} || true"
                    //sh "/usr/local/bin/trivy image --output trivy_report.html $formatOption --timeout 10m --severity CRITICAL --light ${IMAGE} || true"
                    sh "/usr/local/bin/trivy image --output trivy_report.html --format template --template '@html.tpl' --timeout 10m --light ${IMAGE} || true"
                }
                publishHTML(target: [
                    allowMissing: true,
                    alwaysLinkToLastBuild: false,
                    keepAll: true,
                    reportDir: ".",
                    reportFiles: "trivy_report.html",
                    reportName: "Trivy Report",
                ])
            }
        }
    }
}
