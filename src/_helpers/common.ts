import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import { v4 as uuidv4 } from "uuid";
import qs from "querystring";

export async function getApi<T = any>(
  url: string,
  option: AxiosRequestConfig,
  query?: any
) {
  try {
    url = url + (query ? "?" + qs.stringify(query) : "");
    const rs = await axios.get(url, option);
    return rs as AxiosResponse<T>;
  } catch (error) {
    console.log(`[GET][ERROR]url ${url}, error ${error.message}`);
    throw error;
  }
}

export async function postApi<T = any>(
  url: string,
  data: any,
  option: AxiosRequestConfig
) {
  try {
    const rs = await axios.post(url, data, option);
    console.log(
      `[POST][INFO]url ${url}, req: ${JSON.stringify(
        data
      )}, res: ${JSON.stringify(rs.data || {})}`
    );
    return rs as AxiosResponse<T>;
  } catch (error) {
    console.log(
      `[POST][ERROR]url ${url}, req: ${JSON.stringify(data)}, error ${
        error.message
      }`
    );
    throw error;
  }
}
