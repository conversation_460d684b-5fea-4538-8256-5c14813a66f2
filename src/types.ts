import { InferAttributes, Model, Model<PERSON>tatic, NonAttribute, Sequelize } from "sequelize/types";

export type DbAll = {
  log_transactions?: ModelStatic<LogTransactionModel>;
  tasks?: ModelStatic<TaskModel>;
  loan_unsign_doc?: ModelStatic<LoanUnsignDocModel>;
  loan_contract_document?: ModelStatic<LoanContractDocumentModel>;
  los_loan_contract_document?: ModelStatic<LOSLoanContractDocumentModel>;
  esigning?: ModelStatic<EsigningModel>;

  sequelize?: Sequelize;
};

export interface LogTransactionModel extends Model {
  apiName: string;
  body: string;
  response: string;
  responseCode: string;
  contractNumber: string;
  message: string;
  des: string;
  timeRun: number;
  usersID: number;
  ipRequest: string;
  serverHost: string;
}

export interface LoanUnsignDocModel extends Model {
  contract_number: string;
  file_name: string;
  file_path: string;
  url: string;
}

export interface LoanContractDocumentModel extends Model {
  contract_number: string;
  doc_id: string;
  doc_type: string;
  file_name: string;
  file_path: string;
  url: string;
  doc_group: string;
}

export interface LOSLoanContractDocumentModel extends Model {
  contract_number: string;
  doc_id: string;
  doc_type: string;
  file_name: string;
  file_key: string;
  url: string;
  doc_group: string;
}

export interface TaskModel extends Model {
  task_name: string;
  body: string;
  status: number;
  message: string;
  startedAt: Date;
  endedAt: Date;
}

export interface EsigningModel extends Model {
  contractNumber: string;
  losType: string;
  prefix: string;
  signedPrefix: string;
  fileName: string;
  location: string;
  isCallback: boolean;
  identityCard: string;
  status: number;
  firsSignedLocation: string;
  secondSignedLocation: string;
  docType: string;
  createdAt: Date;
  updatedAt: Date;
}
