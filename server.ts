import constants from "./src/_helpers/constants";

import dotenv from "dotenv";
dotenv.config({ path: `.env.${process.env.NODE_ENV}` });
import express from "express";
const app = express();
app.disable("x-powered-by");
import cors from "cors";
import bodyParser from "body-parser";
import errorHandler from "./src/_middleware/error-handler";
import { logRequest } from "./src/log_transaction/log_transaction.service";
import i18nMiddleware from "./i18n";
import "./src/_helpers/db";
const Queue = require("bull");
const uuid = require("uuid");

import convertRouter from "./src/convert/convert.controller";

import esigningRouter from "./src/esigning/esigning.controller";

import * as esigning from "./src/esigning/esigning.service";
import * as common from "./src/_helpers/common";

//get config
new Promise(async () => {
  try {
    const url = process.env.HOST_CONFIGURATION + "/services/?service=convert_service";
    const rs = await common.getApi(url, { headers: { "Content-Type": "application/json" } });
    if (!rs) {
      console.log("Cannot load config");
    } else {
      const config = rs?.data?.data;
      global.basic = rs?.data?.basic;
      global.config = config;
    }
  } catch (error) {
    console.log(`Cannot load config ${error}`);
  }
});

const { CONFIG_URL } = constants;

app.use(i18nMiddleware);

app.use(bodyParser.urlencoded({ extended: false }));
app.use(
  bodyParser.json({
    limit: "50mb",
  })
);
app.use(
  bodyParser.text({
    type: "application/pgp-encrypted",
  })
);

app.use(
  cors({
    origin: "easycredit.vn",
  })
);

app.use(logRequest);

// api routes
app.use(`${CONFIG_URL.PREFIX_URL}/convert`, convertRouter);

app.use(`${CONFIG_URL.PREFIX_URL}/esigning`, esigningRouter);

import moment from "moment";
import { convert2esign2push, convert2push } from "./src/_helpers/func";
import url from "url";
import db from "./src/_helpers/db";
import { resolve } from "path";
app.get(
  `${CONFIG_URL.PREFIX_URL}/healthcheck`,
  require("express-healthcheck")({
    healthy: function () {
      return {
        everything: `convert service is okeeee`,
        now: moment(new Date()).utcOffset("+0700").format("YYYY-MM-DD HH:mm:ss"),
      };
    },
  })
);

// global error handler
app.use(errorHandler);

const CONCURRENT_TASKS = 2;
global._taskQueue = new Queue("gen-contract-queue", {
  redis: { host: process.env.REDIS_HOST, port: process.env.REDIS_PORT }, // Redis configuration
});

global._taskQueue.process(CONCURRENT_TASKS, async (job) => {
  const { task_name, body } = job.data;
  console.log(`Processing task: ${task_name} with payload:${JSON.stringify(body)}`);

  switch (task_name) {
    case constants.TASK_NAMES.GEN_EVC_CONTRACT_FILE:
      await doGenEvcContractFile(body);
      break;
    case constants.TASK_NAMES.GEN_EVC_ESIGNING_CONTRACT_FILE:
      await doGenEvcEsigningContractFile(body);
      break;
    case constants.TASK_NAMES.GEN_EVC_TERM_POLICY:
      await doGenEvcTermPolicy(body);
      break;
    case constants.TASK_NAMES.GEN_DNSE_TTRV:
      await doGenDnseTtrv(body);
      break;
    case constants.TASK_NAMES.GEN_DNSE_BCTD:
      await doGenDnseBctd(body);
      break;
    case constants.TASK_NAMES.GEN_GIMO_BCTD:
      await doGenFundBctd(body);
      break;
    case constants.TASK_NAMES.GEN_FUND_BCTD:
      await doGenGimoBctd(body);
      break;
    case constants.TASK_NAMES.GEN_DNSE_LD:
      await doGenDnseLd(body);
      break;
    case constants.TASK_NAMES.GEN_GIMO_LD:
      await doGenGimoLd(body);
      break;
    case constants.TASK_NAMES.GEN_FUND_LD:
      await doGenFundLd(body);
      break;
    case constants.TASK_NAMES.GEN_FUND_DDH:
      await doGenFundDdh(body);
      break;
    case constants.TASK_NAMES.GEN_CUSTOMER_ESIGNING_FILE:
      await doGenCustomerEsigningFile(body);
      break;
    case constants.TASK_NAMES.GEN_MANF_BCTD:
      await doGenManfBctd(body);
      break;
    case constants.TASK_NAMES.GEN_MANF_LD:
      await doGenManfLd(body);
      break;
    case constants.TASK_NAMES.GEN_MANF_ESIGNING_CONTRACT_FILE:
      await doGenManfEsigningContractFile(body);
      break;
    case constants.TASK_NAMES.GEN_BTT_CONTRACT_FILE:
      await doGenBTTDocumentFile(body);
      break;
    default:
      throw `not support task ${task_name}!`;
  }

  console.log(`Task "${task_name}" completed.`);
});

async function doGenBTTDocumentFile(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate SellerCreditLimitContract ${contract_number}`);

    let fileLocation = await convert2push(file_tem_path, contract_data, "/mc-los/btt-document", file_name);
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate SellerCreditLimitContract ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();

    const document = await db.los_loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "Test",
      file_name: file_name,
      file_key: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save SellerCreditLimitContract document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenSellerCreditLimitContract`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenEvcContractFile(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, file_tem_path_unsigned, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    console.log(`Start generate file contract ${contract_number}`);

    const fileLocationSigned = await convert2push(file_tem_path, contract_data, "/los-united/signed-contract", file_name);
    let fileLocationUnsigned;
    if (file_tem_path_unsigned && file_tem_path_unsigned != file_tem_path) {
      fileLocationUnsigned = await convert2push(file_tem_path_unsigned, contract_data, "/los-united/unsigned-contract", file_name);
      if (!fileLocationUnsigned) {
        throw "convert unsigned fail!";
      }
    }
    if (!fileLocationSigned) {
      throw "convert signed fail!";
    }

    console.log(`Generate file contract ${contract_number} successfully, file location: ${fileLocationSigned}`);
    const dataSaveSignedDoc = {
      contract_number: contract_number,
      file_name: file_name,
      file_path: url.parse(fileLocationSigned.toString()).path.slice(1),
      url: fileLocationSigned,
    };
    const dataSaveUnsignDoc = {
      contract_number: contract_number,
      file_name: file_name,
      file_path: fileLocationUnsigned ? url.parse(fileLocationUnsigned.toString()).path.slice(1) : dataSaveSignedDoc.file_path,
      url: fileLocationUnsigned ? fileLocationUnsigned : dataSaveSignedDoc.url,
    };

    const unsignDoc = await db.loan_unsign_doc.create(dataSaveUnsignDoc);
    const docIdLCT = uuid.v4();
    if (!unsignDoc) {
      throw "save unsign doc fail!";
    }

    const loanContractDocument = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "LCT",
      file_name: file_name,
      file_path: dataSaveSignedDoc.file_path,
      url: dataSaveSignedDoc.url,
      doc_group: null,
    });
    if (!loanContractDocument) {
      throw "save loan contract document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenEvcContractFile`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}
async function doGenEvcEsigningContractFile(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate file contract ${contract_number}`);

    let fileLocation = await convert2push(file_tem_path, contract_data, "/los-united/unsigned-contract", file_name);
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate file contract ${contract_number} successfully, file location: ${fileLocation}`);
    let dataSaveUnsignDoc = {
      contract_number: contract_number,
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
    };

    const unsignDoc = await db.loan_unsign_doc.create(dataSaveUnsignDoc);
    if (!unsignDoc) {
      throw "save unsign doc fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenEvcEsigningContractFile`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenManfEsigningContractFile(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate file contract ${contract_number}`);

    let fileLocation = await convert2push(file_tem_path, contract_data, "/los-united/unsigned-contract", file_name);
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate file contract ${contract_number} successfully, file location: ${fileLocation}`);
    let dataSaveUnsignDoc = {
      contract_number: contract_number,
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
    };

    const unsignDoc = await db.loan_unsign_doc.create(dataSaveUnsignDoc);
    if (!unsignDoc) {
      throw "save unsign doc fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenManfEsigningContractFile`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenEvcTermPolicy(taskBody) {
  try {
    const { file_name, full_name, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    console.log(`Start generate term policy ${full_name}`);

    let fileLocation = await convert2push(file_tem_path, contract_data, "/los-united/term-policy", file_name);
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate temp_policy ${full_name} successfully, file location: ${fileLocation}`);

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenEvcTermPolicy`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenDnseTtrv(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate ttrv ${contract_number}`);

    let fileLocation = await convert2push(file_tem_path, contract_data, "/los-united/ttrv", file_name);
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate ttrv ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();
    const document = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "TTRV",
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save ttrv document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenDnseTtrv`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenDnseBctd(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate bctd ${contract_number}`);

    let fileLocation = await convert2esign2push(file_tem_path, contract_data, "/los-united/bctd", file_name, "BCTD", "dnse");
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate bctd ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();
    const document = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "SPAR",
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save bctd document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenDnseBctd`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenFundBctd(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate bctd ${contract_number}`);

    let fileLocation = await convert2esign2push(file_tem_path, contract_data, "/los-united/bctd", file_name, "BCTD", "fund");
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate bctd ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();
    const document = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "SPAR",
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save bctd document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenFundBctd`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenDnseLd(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate ld ${contract_number}`);

    let fileLocation = await convert2esign2push(file_tem_path, contract_data, "/los-united/ld", file_name, "LD", "dnse");
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate ld ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();
    const document = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "LD",
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save ld document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenDnseLd`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenGimoBctd(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate bctd ${contract_number}`);

    let fileLocation = await convert2esign2push(file_tem_path, contract_data, "/los-united/bctd", file_name, "BCTD", "gimo");
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate bctd ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();
    const document = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "SPAR",
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save bctd document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenGimoBctd`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenGimoLd(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate ld ${contract_number}`);

    let fileLocation = await convert2esign2push(file_tem_path, contract_data, "/los-united/ld", file_name, "LD", "gimo");
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate ld ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();
    const document = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "LD",
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save ld document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenGimoLd`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenFundLd(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate ld ${contract_number}`);

    let fileLocation = await convert2esign2push(file_tem_path, contract_data, "/los-united/ld", file_name, "LD", "fund");
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate ld ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();
    const document = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "LD",
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save ld document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenFundLd`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenFundDdh(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate ddh ${contract_number}`);

    let fileLocation = await convert2esign2push(file_tem_path, contract_data, "/los-united/odevg", file_name, "ODEVG", "fund");
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate ddh ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();
    const document = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "ODEVG",
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save ddh document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenFundDdh`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenCustomerEsigningFile(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data, los_type, is_callback, doc_type } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    console.log(`Start doGenCustomerEsigningFile ${contract_number}`);
    const prefix = taskBody.prefix || "/los-united/unsigned-contract";
    const signedPrefix = taskBody.signedPrefix || "/los-united/signed-contract";
    let fileLocation = await convert2push(file_tem_path, contract_data, prefix, file_name);
    if (!fileLocation) {
      throw "convert fail!";
    }
    const { identity_card } = contract_data;
    const unsignDoc = await db.esigning.create({
      isCallback: is_callback || false,
      prefix,
      signedPrefix,
      contractNumber: contract_number,
      losType: los_type,
      location: fileLocation,
      fileName: file_name,
      identityCard: identity_card,
      status: constants.SIGN_STATUS.INIT,
      docType: doc_type,
    });
    if (!unsignDoc) {
      throw "save unsign doc fail!";
    }

    console.log(`doGenCustomerEsigningFile ${contract_number} successfully, file location: ${fileLocation}`);
    //callback
    if (is_callback) {
      esigning.callbackTask(contract_number);
    }
    return true;
  } catch (e) {
    console.log({
      functionName: `doGenCustomerEsigningFile`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenManfBctd(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate bctd ${contract_number}`);

    let fileLocation = await convert2esign2push(file_tem_path, contract_data, "/los-united/bctd", file_name, "BCTD", "manf");
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate bctd ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();
    const document = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "SPAR",
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save bctd document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenManfBctd`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

async function doGenManfLd(taskBody) {
  try {
    const { file_name, contract_number, file_tem_path, contract_data } = taskBody;

    if (!file_tem_path) {
      throw "file_tem_path not valid!";
    }

    if (!contract_data) {
      throw "contract data not valid!";
    }

    console.log(`Start generate ld ${contract_number}`);

    let fileLocation = await convert2esign2push(file_tem_path, contract_data, "/los-united/ld", file_name, "LD", "manf");
    if (!fileLocation) {
      throw "convert fail!";
    }

    console.log(`Generate ld ${contract_number} successfully, file location: ${fileLocation}`);

    const docIdLCT = uuid.v4();
    const document = await db.loan_contract_document.create({
      contract_number: contract_number,
      doc_id: docIdLCT,
      doc_type: "LD",
      file_name: file_name,
      file_path: url.parse(fileLocation.toString()).path.slice(1),
      url: fileLocation,
      doc_group: null,
    });

    if (!document) {
      throw "save ld document fail!";
    }

    return true;
  } catch (e) {
    console.log({
      functionName: `doGenManfLd`,
      errorMessage: e.message,
    });
    console.error(e);

    return false;
  }
}

global._taskQueue.on("failed", (job, err) => {
  console.error(`Task "${job.data.task_name}" failed with error:`, err);
});

// start server
const port = process.env.PORT;
const server = app.listen(port, () => {
  console.log("Server listening on port " + port);
});

export default server;
